import threading
import time
from datetime import datetime
from flask import app
from application.Models.MachineCommand import Machine<PERSON>ommand,find_pending_command,update_command_status,update_machine_command,update_machine_command_o
from application.Models.Device import Device,get_device_by_serial_num
from application.web_socket.WebSocketPool import WebSocketPool,ws_device

class SendOrderJob(threading.Thread):
    def __init__(self):
        threading.Thread.__init__(self)
        self.stop_event = threading.Event()
        # self.machine_command_mapper = machine_command_mapper
        # self.device_mapper = device_mapper
        self.wd_list = {}  # WebSocketPool.wsDevice
        self._thread=None
    

    def run_job(self):
        from app import app, db_connection
        from application.Models.company import CompanyDevice
        app.logger.info("SendOrderJob start running")
        while not self.stop_event.is_set():
            # print("SendOrderJob start running-----"+datetime.now().strftime('%Y-%m-%d %H:%M:%S') +" wd_list len:"+ str(len(self.wd_list)))
            for key, device_status in self.wd_list.items():
                items = self.wd_list.items()
                app.logger.info(f"SendOrderJob start running-----{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} wd_list len:{len(self.wd_list)}")
                app.logger.info(f"items return:{items}")
                with app.app_context():
                    app.logger.info(f"The status of the device : {device_status}")
                    try:
                        app.logger.info(f"key which is the device serial number: {key}")
                        # Get the right database
                        try:
                            database_name = CompanyDevice.get_database_name_by_sn(key)
                            app.logger.info(f"database_name: {database_name}")
                        except Exception as e:
                            app.logger.error(f"Failed to get database name: {e}")
                            continue
                        # connect to the database
                        with db_connection.get_session(database_name) as session:

                            try:
                                in_sending = find_pending_command(session, 0, key)
                                app.logger.info(f"in_sending after passing the session: {in_sending}")
                            except Exception as e:
                                app.logger.error(f"Failed to find pending command: {e}")
                                
                            # print("in_sending",in_sending)
                            if in_sending:
                                app.logger.info(f"inside the if condition in_sending: {in_sending}")
                                try:
                                    pending_command = find_pending_command(session, 1, key)
                                    app.logger.info(f"This is the  pending_command: {pending_command}")
                                except Exception as e:
                                    app.logger.error(f"Failed to find pending command: {e}")
                                if not pending_command:
                                    app.logger.info(" no pending_command found")
                                    if isinstance(device_status, dict):
                                        app.logger.info(f"device_status is of instance dictionary: {device_status}")
                                        websock = device_status['websocket']
                                        app.logger.info(f" web socket: {websock}")
                                        try:
                                            app.logger.info(f"Sending the content:")
                                            sent = websock.send(in_sending[0].content)
                                            app.logger.info(f"return value of send: {sent}")
                                        except Exception as e:
                                            app.logger.error(f"Failed to send content: {str(e)}")
                                    else:
                                        app.logger.info(f"device_status is not of instance dictionary: {device_status}")
                                        websock = device_status.websocket
                                        app.logger.info(f" web socket: {websock}")
                                        try:
                                            app.logger.info(f"Sending the content:")
                                            sent = websock.send(in_sending[0].content)
                                            app.logger.info(f"return value of send: {sent}")
                                        except Exception as e:
                                            app.logger.error(f"Failed to send content: {str(e)}")
                                            websock.send(in_sending[0].content)

                                    print(" not pending_command send")
                                    now = datetime.fromtimestamp(time.time())
                                    try:
                                        updating = update_command_status(session,0, 1, now,in_sending[0].id)
                                        app.logger.info(f"updating the command status: {updating}")
                                    except Exception as e:
                                        app.logger.error(f"Failed to update command status: {e}")
                                elif len(pending_command) == 1:
                                    print(pending_command[0].run_time)
                                    # run_time = datetime.strptime(pending_command[0].run_time, '%Y-%m-%d %H:%M:%S')
                                    run_time =pending_command[0].run_time
                                    now = datetime.fromtimestamp(time.time())
                                    print(run_time)
                                    print(now)
                                    difference = (now - run_time).total_seconds()
                                    print(difference)
                                    if difference > 20: #20

                                        if pending_command[0].err_count < 3:
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            machine_command.run_time = now

                                            try:
                                                app.logger.info(f"Updating the machine command: {machine_command}")
                                                updated_command = update_machine_command_o(session, machine_command)
                                                app.logger.info(f"Updated command: {updated_command}")
                                            except Exception as e:
                                                app.logger.error(f"Failed to update machine command: {e}")
                                            try:
                                                app.logger.info(f"Getting the device by serial number: {pending_command[0].serial}")
                                                device = get_device_by_serial_num(session, pending_command[0].serial)
                                                app.logger.info(f"Device retrieved: {device}")
                                            except Exception as e:
                                                app.logger.error(f"Failed to get device by serial number: {e}")
                                            app.logger.info("pending_command[0].err_count < 3")
                                            if device.status != 0:
                                                print(" device.status != 0")
                                                print("device_status1")
                                                #print(device_status)
                                                if isinstance(device_status,dict):
                                                    websock = device_status['websocket']
                                                    print(websock)
                                                    websock.send(pending_command[0].content)
                                                else:
                                                    websock = device_status.websocket
                                                    print(websock)
                                                    websock.send(pending_command[0].content)
                                        else:
                                            app.logger.info("pending_command[0].err_count > 3")
                                            app.logger.info(f"Device: {device}")
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            update_machine_command_o(session, machine_command)
                                    else:
                                        app.logger.info(f"difference < 20: {str(difference)}")
                            else:
                                app.logger.info(f"in_sending: {in_sending}")
                                pending_command = find_pending_command(session, 1, key)
                                if pending_command:
                                    if (datetime.now() - pending_command[0].run_time).total_seconds() > 20:
                                        if pending_command[0].err_count < 3:
                                            now = datetime.fromtimestamp(time.time())
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            machine_command.run_time = now
                                            update_machine_command_o(session, machine_command)
                                            device = get_device_by_serial_num(session, pending_command[0].serial)
                                            if device.status != 0:
                                                print("device_status2")
                                                if isinstance(device_status, dict):
                                                    websock = device_status['websocket']
                                                    print(websock)
                                                    websock.send(pending_command[0].content)
                                                else:
                                                    websock = device_status.websocket
                                                    print(websock)
                                                    websock.send(pending_command[0].content)

                                        else:
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            update_machine_command_o(session,machine_command)

                    except Exception as e:
                        import traceback
                        traceback.print_exc()
                        print(e)

            time.sleep(1)  # Avoid high CPU usage

    def stop(self):
        self.stop_event.set()

    # def start_thread(self):
    #     self.start()

    def start_thread(self):
        if self._thread is None or not self._thread.is_alive():
            print("SendOrderJob start running-start_thread")
            self.wd_list=ws_device
            self._thread = threading.Thread(target=self.run_job)
            self._thread.start()

    def is_running(self):
        return self._thread is not None and self._thread.is_alive()
    def stop_thread(self):
        self.stop()
